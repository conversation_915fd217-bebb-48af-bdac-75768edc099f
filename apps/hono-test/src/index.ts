import { serve } from '@hono/node-server';
import { Hono } from 'hono';
import { createReAuthEngine } from '@the-forgebase/reauth/auth-engine';
import EmailPasswordAuth from '@the-forgebase/reauth/plugins/email-password';
import type {
  AuthOutput,
  AuthPlugin,
  AuthToken,
  CradleService,
  Entity,
  SessionService,
} from '@the-forgebase/reauth/types';
import { showRoutes } from 'hono/dev';
import { getCookie, setCookie, deleteCookie } from 'hono/cookie';
import knex from 'knex';

const app = new Hono();

const db = knex({
  client: 'better-sqlite3',
  connection: {
    filename: './test.db',
  },
  useNullAsDefault: true,
});

const reAuth = createReAuthEngine([EmailPasswordAuth], {
  database: 'knex',
  knexdb: db,
});

app.use(async (c, next) => {
  c.set('entity', null);
  c.set('token', null);
  const token = getCookie(c, 'token');
  if (token) {
    const container = reAuth.getContainer();
    const sessionService: CradleService<'sessionService'> =
      container.cradle.sessionService;
    const { entity, token: newToken } = await sessionService.verifySession(
      token as string,
    );

    if (!entity || !newToken) {
      // Invalid session, clear cookie
      deleteCookie(c, 'token');
      return next();
    }

    c.set('entity', entity);
    c.set('token', newToken);
    setCookie(c, 'token', newToken);
  }
  await next();
});

const auth = new Hono();

// list of plugins and there names with steps
const authRoutes = reAuth.getAllPlugins().map((plugin: AuthPlugin) => {
  return {
    name: plugin.name,
    steps: plugin.steps.map((step) => ({
      name: step.name,
      protocol: step.protocol,
    })),
  };
});

// each step is a route
authRoutes.forEach((route) => {
  route.steps.forEach((step) => {
    // If no http protocol, skip
    if (!step.protocol?.http) return;
    auth.on(
      [step.protocol.http.method],
      `/${route.name}/${step.name}`,
      async (c) => {
        const expectedInputs = reAuth.getStepInputs(route.name, step.name);
        let inputs: Record<string, any> = {};
        const body: Record<string, any> = await c.req.json();

        //1. find the expected inputs in the body
        expectedInputs.forEach((input) => {
          if (body[input]) {
            inputs[input] = body[input];
          }
        });

        //2. find the expected inputs in the query params
        expectedInputs.forEach((input) => {
          if (c.req.query(input)) {
            inputs[input] = c.req.query(input);
          }
        });

        if (step.protocol.http?.auth) {
          const entity = c.get('entity');
          const token = c.get('token');
          if (!entity) {
            return c.json({ error: 'Unauthorized' }, 401);
          }
          inputs.entity = entity;
          inputs.token = token;
        }

        if (Object.keys(inputs).length !== expectedInputs.length) {
          return c.json({ error: 'Missing inputs' }, 400);
        }

        const {
          token,
          redirect: rd,
          ...rest
        }: AuthOutput = await reAuth.executeStep(route.name, step.name, inputs);

        if (token) {
          // save cookie
          setCookie(c, 'token', token);
        }

        if (rd) {
          return c.redirect(rd);
        }

        return c.json(rest);
      },
    );
  });
});

app.route('/auth', auth);

app.get('/', (c) => {
  return c.text('Hello Hono!');
});

showRoutes(app, {
  verbose: true,
});

serve(
  {
    fetch: app.fetch,
    port: 3000,
  },
  (info) => {
    console.log(`Server is running on http://localhost:${info.port}`);
  },
);

declare module 'hono' {
  interface ContextVariableMap {
    entity: Entity | null;
    token: AuthToken | null;
  }
}

const test: Entity = {
  id: '1',
  role: 'user',
  created_at: new Date(),
  updated_at: new Date(),
  email: '<EMAIL>',
  email_verified: false,
};
