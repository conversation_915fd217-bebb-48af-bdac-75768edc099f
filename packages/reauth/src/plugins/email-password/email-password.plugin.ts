import { type } from 'arktype';
import { AuthPlugin } from '../../types';
import { createStandardSchemaRule } from '../../utils';
import { executeStep } from '../../utils/execute-step';
import { createHookRegisterer } from '../../utils/hook-utils';
import {
  hashPassword,
  haveIbeenPawned,
  verifyPasswordHash,
} from '../../lib/password';

const emailSchema = type('string.email');
const passwordSchema = type('string.alphanumeric >= 8');

const loginValidation = {
  email: createStandardSchemaRule(
    emailSchema,
    'Please enter a valid email address',
  ),
  password: createStandardSchemaRule(
    passwordSchema,
    'Password must be at least 8 characters',
  ),
};

const EmailPasswordAuth: AuthPlugin = {
  name: 'email-password',
  steps: [
    {
      name: 'login',
      description: 'Authenticate user with email and password',
      validationSchema: loginValidation,
      run: async function (input, pluginProperties) {
        const { container } = pluginProperties!;
        const { email, password } = input;
        // Implement login logic here
        const entity = await container.cradle.entityService.findEntity(
          email,
          'email',
        );

        if (!entity) {
          return { success: false, message: 'User not found' };
        }

        const passwordMatch = await verifyPasswordHash(
          entity.password_hash,
          password,
        );

        if (!passwordMatch) {
          return { success: false, message: 'Invalid password' };
        }

        const token = await container.cradle.sessionService.createSession(
          entity.id,
        );

        return { success: true, message: 'Login successful', token };
      },
      registerHook: function (type, fn) {
        if (!this.hooks) this.hooks = {};
        const register = createHookRegisterer(this.hooks);
        register(type, fn);
      },
      hooks: {},
      inputs: ['email', 'password'],
      protocol: {
        http: {
          method: 'POST',
        },
      },
    },
    {
      name: 'register',
      description: 'Register a new user with email and password',
      validationSchema: loginValidation,
      run: async function (input, pluginProperties) {
        const { container } = pluginProperties!;
        const { email, password } = input;

        const savePassword = await haveIbeenPawned(password);

        if (!savePassword) {
          return { success: false, message: 'Password has been pawned' };
        }

        const entity = await container.cradle.entityService.createEntity({
          email,
          password_hash: await hashPassword(password),
          email_verified: false,
        });

        const token = await container.cradle.sessionService.createSession(
          entity.id,
        );

        return { success: true, message: 'Register successful', token };
      },
      registerHook: function (type, fn) {
        if (!this.hooks) this.hooks = {};
        const register = createHookRegisterer(this.hooks);
        register(type, fn);
      },
      hooks: {},
      inputs: ['email', 'password'],
      protocol: {
        http: {
          method: 'POST',
        },
      },
    },
  ],
  getStep(step) {
    return this.steps.find((s) => s.name === step);
  },
  initialize: async function (container) {
    this.container = container;
  },
  runStep: async function (step, input, container) {
    return executeStep(step, input, {
      pluginName: this.name,
      getStep: this.getStep.bind(this),
      container,
    });
  },
};

export default EmailPasswordAuth;

declare module '../../types' {
  interface EntityExtension {
    email: string;
    password_hash: string;
    email_verified: boolean;
  }
}
