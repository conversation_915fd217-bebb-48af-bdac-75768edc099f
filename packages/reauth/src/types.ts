import { AwilixContainer } from 'awilix';
import { Knex } from 'knex';
export type CookieSameSite = 'lax' | 'strict' | 'none';

export interface ValidationResult {
  isValid: boolean;
  errors?: Record<string, string> | undefined;
}

export type ValidationRule<T = any> = (
  value: T,
  input: AuthInput,
) => string | undefined;

export type ValidationSchema = Record<
  string,
  ValidationRule | ValidationRule[]
>;

type StepInputHook<T> = (
  input: T,
  container: AwilixContainer<ReAuthCradle>,
) => T | Promise<T>;
type StepOutputHook<T> = (
  output: T,
  container: AwilixContainer<ReAuthCradle>,
) => T | Promise<T>;

export interface AuthStepHooks {
  before?: StepInputHook<AuthInput>;
  after?: StepOutputHook<AuthOutput>;
  onError?: (
    error: Error,
    input: AuthInput,
    container: AwilixContainer<ReAuthCradle>,
  ) => Promise<void> | void;
}

export type PluginProp = {
  pluginName: string;
  container: AwilixContainer<ReAuthCradle>;
};

export interface AuthStep {
  name: string;
  description: string;
  validationSchema?: ValidationSchema;
  inputs: string[];
  hooks?: AuthStepHooks;
  registerHook(
    type: HooksType,
    fn: (
      data: AuthInput | AuthOutput,
      container: AwilixContainer<ReAuthCradle>,
      error?: Error,
    ) => Promise<AuthOutput | AuthInput | void>,
  ): void;
  run(input: AuthInput, pluginProperties?: PluginProp): Promise<AuthOutput>;
  protocol: {
    http?: {
      method: string;
      auth?: boolean;
    };
    [key: string]: any;
  };
}

export type AuthInput = {
  entity?: Entity;
  token?: AuthToken;
} & Record<string, any>;

export type HooksType = 'before' | 'after' | 'onError';

export interface BaseReAuthCradle {
  entityService: EntityService;
  sessionService: SessionService;
  knex: Knex;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface ReAuthCradleExtension {}

export type ReAuthCradle = BaseReAuthCradle & ReAuthCradleExtension;

export type CradleService<T extends keyof ReAuthCradle> = ReAuthCradle[T];

export interface AuthPlugin {
  name: string;
  steps: AuthStep[];
  container?: AwilixContainer<ReAuthCradle>;
  /**
   * Initialize the plugin with an optional DI container
   * @param container Optional Awilix container for dependency injection
   */
  initialize(container: AwilixContainer<ReAuthCradle>): Promise<void> | void;

  getStep(step: string): AuthStep | undefined;

  runStep(
    step: string,
    input: AuthInput,
    container: AwilixContainer<ReAuthCradle>,
  ): Promise<AuthOutput>;
}

export class ConfigError extends Error {
  constructor(
    message: string,
    public pluginName: string,
    public data?: any,
  ) {
    super(message);
    this.name = 'ConfigError';
  }
}

export class AuthInputError extends Error {
  constructor(
    message: string,
    public pluginName: string,
    public stepName: string,
    public data?: any,
  ) {
    super(message);
    this.name = 'AuthInputError';
  }
}

export class StepNotFound extends Error {
  constructor(
    step: string,
    public pluginName: string,
  ) {
    super(`Step ${step} not found for plugin ${pluginName}`);
    this.name = 'StepNotFound';
  }
}

export class ValidationError extends Error {
  constructor(
    message: string,
    public pluginName: string,
    public stepName: string,
    public hookType?: HooksType,
    public data?: any,
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class PluginNotFound extends Error {
  constructor(plugin: string) {
    super(`Plugin ${plugin} not found`);
    this.name = 'PluginNotFound';
  }
}

export class HooksError extends Error {
  data?: any;

  constructor(
    message: string,
    public pluginName: string,
    public stepName: string,
    public hookType: HooksType,
    data?: any,
  ) {
    super(message);
    this.name = 'HooksError';
    this.data = data;
  }
}

export class InitializationError extends Error {
  constructor(
    message: string,
    public pluginName: string,
    public data?: any,
  ) {
    super(message);
    this.name = 'InitializationError';
  }
}

export type BaseEntity = {
  id: string;
  role: string;
  created_at: Date;
  updated_at: Date;
};

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface EntityExtension {}

// Generic type for custom user fields
export type Entity = BaseEntity & EntityExtension;

export interface BaseSession {
  id: string;
  entity_id: string;
  token: string;
  expires_at: Date;
  created_at: Date;
  updated_at: Date;
}

export interface SessionExtension {}

export type Session = BaseSession & SessionExtension;

export type AuthToken = string | null;

export type AuthOutput = {
  entity?: Entity;
  token?: AuthToken;
  redirect?: string;
  success: boolean;
  message: string;
} & Record<string, any>;
export type EntityService = {
  findEntity(id: string, filed: string): Promise<Entity | null>;
  createEntity(entity: Partial<Entity>): Promise<Entity>;
  updateEntity(
    id: string,
    filed: string,
    entity: Partial<Entity>,
  ): Promise<Entity>;
  deleteEntity(id: string, filed: string): Promise<void>;
};

export type SessionService = {
  initialize?(): Promise<void>;
  createSession(entityId: string | number): Promise<AuthToken>;
  verifySession(
    token: string,
  ): Promise<{ entity: Entity | null; token: AuthToken }>;
  destroySession(token: string): Promise<void>;
  destroyAllSessions(entityId: string | number): Promise<void>;
};
