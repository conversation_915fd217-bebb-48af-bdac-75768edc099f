import {
  AuthInput,
  AuthOutput,
  AuthPlugin,
  Entity,
  HooksType,
  PluginNotFound,
  ReAuthCradle,
  StepNotFound,
} from './types';
import {
  createContainer,
  InjectionMode,
  AwilixContainer,
  asValue,
  asClass,
} from 'awilix';
import { KnexEntityService } from './services/knex-entity-service';
import { KnexSessionService } from './services/knex.session-service';
import knex, { Knex } from 'knex';

export class ReAuthEngine {
  private container: AwilixContainer<ReAuthCradle>;
  private plugins: AuthPlugin[] = [];

  constructor(
    initialPlugins: AuthPlugin[] = [],
    config: {
      customServices?: {
        entityService?: boolean;
        sessionService?: boolean;
      };
      database: string;
      knexdb?: Knex;
      knexConfig?: Knex.Config;
    },
  ) {
    this.container = createContainer<ReAuthCradle>({
      injectionMode: InjectionMode.CLASSIC,
      strict: true,
    });

    if (config.database === 'knex' && config.knexdb) {
      this.container.register({
        knex: asValue(config.knexdb),
      });
    } else if (config.database === 'knex' && config.knexConfig) {
      this.container.register({
        knex: asValue(knex(config.knexConfig)),
      });
    } else if (
      config.database === 'knex' &&
      (!config.knexdb || !config.knexConfig)
    ) {
      throw new Error('Invalid database configuration');
    }

    if (!config.customServices?.entityService) {
      this.container.register({
        entityService: asClass(KnexEntityService)
          .singleton()
          .inject(() => ({
            tableName: 'entities',
            knex: this.container.resolve('knex'),
          })),
      });
    }
    if (!config.customServices?.sessionService) {
      this.container.register({
        sessionService: asClass(KnexSessionService)
          .singleton()
          .inject(() => ({
            tableName: 'sessions',
            knex: this.container.resolve('knex'),
          })),
      });
    }

    initialPlugins.forEach((plugin) => this.registerPlugin(plugin));
  }

  /**
   * Get a service from the DI container
   * @param name The name of the service to retrieve
   */
  getService<T extends keyof ReAuthCradle>(
    container: AwilixContainer<ReAuthCradle>,
    serviceName: T,
  ): ReAuthCradle[T] {
    return container.cradle[serviceName];
  }

  /**
   * Register an auth plugin
   * @param auth The auth plugin to register
   */
  registerPlugin(auth: AuthPlugin) {
    this.plugins.push(auth);
    auth.initialize(this.container);
    return this;
  }

  /**
   * Get a plugin by name
   * @param name The name of the plugin to retrieve
   */
  getPlugin(name: string) {
    const plugin = this.plugins.find((p) => p.name === name);
    if (!plugin) {
      throw new PluginNotFound(name);
    }
    return plugin;
  }

  /**
   * Get all registered plugins
   */
  getAllPlugins() {
    return this.plugins;
  }

  /**
   * Get the DI container
   */
  getContainer(): AwilixContainer<ReAuthCradle> {
    return this.container;
  }

  executeStep(pluginName: string, stepName: string, input: AuthInput) {
    const plugin = this.getPlugin(pluginName);
    const step = plugin.steps.find((s) => s.name === stepName);
    if (!step) {
      throw new StepNotFound(stepName, pluginName);
    }
    return plugin.runStep(step.name, input, this.container);
  }

  registerHook(
    pluginName: string,
    stepName: string,
    type: HooksType,
    fn: (
      data: AuthInput | AuthOutput,
      container: AwilixContainer<ReAuthCradle>,
      error?: Error,
    ) => Promise<AuthOutput | AuthInput | void>,
  ) {
    const plugin = this.getPlugin(pluginName);
    const step = plugin.steps.find((s) => s.name === stepName);
    if (!step) {
      throw new StepNotFound(stepName, pluginName);
    }
    step.registerHook(type, fn);
    return this;
  }

  getStepInputs(pluginName: string, stepName: string) {
    const plugin = this.getPlugin(pluginName);
    const step = plugin.steps.find((s) => s.name === stepName);
    if (!step) {
      throw new StepNotFound(stepName, pluginName);
    }
    return step.inputs;
  }
}

export const createReAuthEngine = (
  initialPlugins: AuthPlugin[] = [],
  config: {
    customServices?: {
      entityService?: boolean;
      sessionService?: boolean;
    };
    database: string;
    knexdb?: Knex;
    knexConfig?: Knex.Config;
  },
): ReAuthEngine => {
  return new ReAuthEngine(initialPlugins, config);
};

const test: Entity = {
  id: '1',
  role: 'user',
  created_at: new Date(),
  updated_at: new Date(),
  email: '<EMAIL>',
  email_verified: false,
  password_hash: 'test',
};
