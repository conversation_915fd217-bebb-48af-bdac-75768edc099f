import {
  AuthStep,
  AuthInput,
  AuthOutput,
  AuthInputError,
  StepNotFound,
} from '../types';
import { validateInputWithValidationSchema } from './standard-schema';

export interface ExecuteStepOptions {
  /** Name of the plugin for error messages */
  pluginName: string;
  /** Function to get the step by name */
  getStep: (step: string) => AuthStep | undefined;
  /** Optional DI container */
  container?: any;
}

/**
 * Execute an auth step with validation and hooks
 * @param stepName Name of the step to execute
 * @param input Input data for the step
 * @param options Configuration options
 * @returns Promise that resolves with the step's output
 */
export async function executeStep(
  stepName: string,
  input: AuthInput,
  options: ExecuteStepOptions,
): Promise<AuthOutput> {
  const { pluginName, getStep, container } = options;
  const stepObj = getStep(stepName);

  if (!stepObj) {
    throw new StepNotFound(stepName, pluginName);
  }

  try {
    // Validate input if schema is provided
    if (stepObj.validationSchema) {
      const result = await validateInputWithValidationSchema(
        stepObj.validationSchema,
        input,
      );

      if (!result.isValid) {
        throw new AuthInputError(
          'Input validation failed',
          pluginName,
          stepObj.name,
          result.errors,
        );
      }
    }

    let inp = input;

    // Run before hooks
    if (stepObj.hooks?.before) {
      inp = await stepObj.hooks.before(input, container);
    }

    // Execute the step
    const result = await stepObj.run(inp, {
      pluginName,
      container,
    });

    // Run after hooks
    if (stepObj.hooks?.after) {
      return await stepObj.hooks.after(result, container);
    }

    return result;
  } catch (error) {
    // Run error hooks if provided
    if (stepObj.hooks?.onError) {
      await stepObj.hooks.onError(error as Error, input, container);
    }
    throw error;
  }
}

export default executeStep;
