{"name": "@the-forgebase/reauth", "version": "0.1.0-alpha.1", "type": "module", "main": "./dist/cjs/src/index.js", "module": "./dist/esm/src/index.js", "types": "./dist/esm/src/index.d.ts", "scripts": {"clean": "rm -rf dist", "build:esm": "tsc -p tsconfig.esm.json", "build:cjs": "tsc -p tsconfig.cjs.json", "build": "pnpm clean && pnpm build:esm && pnpm build:cjs", "dev": "pnpm build:esm --watch", "lint": "tsc --noEmit", "test": "vitest", "test:watch": "vitest --watch"}, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/esm/src/index.d.ts", "import": "./dist/esm/src/index.js", "require": "./dist/cjs/src/index.js"}, "./types": {"types": "./dist/esm/src/types.d.ts", "import": "./dist/esm/src/types.js", "require": "./dist/cjs/src/types.js"}, "./auth-engine": {"types": "./dist/esm/src/auth-engine.d.ts", "import": "./dist/esm/src/auth-engine.js", "require": "./dist/cjs/src/auth-engine.js"}, "./plugins/email-password": {"types": "./dist/esm/src/plugins/email-password/email-password.plugin.d.ts", "import": "./dist/esm/src/plugins/email-password/email-password.plugin.js", "require": "./dist/cjs/src/plugins/email-password/email-password.plugin.js"}}, "typesVersions": {"*": {".": ["./dist/esm/src/index.d.ts"], "types": ["./dist/esm/src/types.d.ts"], "auth-engine": ["./dist/esm/src/auth-engine.d.ts"], "plugins/email-password": ["./dist/esm/src/plugins/email-password/email-password.plugin.d.ts"]}}, "files": ["dist", "!**/*.tsbuildinfo", "package.json", "README.md"], "devDependencies": {"@repo/eslint-config": "workspace:*", "@the-forgebase/typescript-config": "workspace:*", "@types/deno": "^2.2.0", "@types/node": "^22.15.21", "tslib": "^2.8.1", "vitest": "^3.1.4"}, "dependencies": {"@node-rs/argon2": "^2.0.2", "@oslojs/crypto": "^1.0.1", "@standard-schema/spec": "^1.0.0", "arktype": "^2.1.20", "awilix": "^12.0.5", "axios": "^1.9.0", "knex": "^3.1.0", "typescript": "^5.8.3", "uuid": "^11.1.0"}}